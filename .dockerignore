# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs/*.log
*.log

# 数据库文件（构建时不包含，运行时挂载）
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 测试文件
test_*.py
*_test.py
tests/

# 文档
*.md
docs/

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 配置文件（运行时挂载）
# global_config.yml

# 其他
.env
.env.local
.env.*.local
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
